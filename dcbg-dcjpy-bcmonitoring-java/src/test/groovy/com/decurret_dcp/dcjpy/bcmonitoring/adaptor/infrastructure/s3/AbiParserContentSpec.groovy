package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.EventDefinition
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParameterInfo
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ResourceNotFoundException
import java.nio.charset.StandardCharsets
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.core.methods.response.Log
import spock.lang.Specification

class AbiParserContentSpec extends Specification {

	AbiParser parser
	BcmonitoringConfigurationProperties propertiesMock

	def setup() {
		propertiesMock = Mock(BcmonitoringConfigurationProperties)
		parser = new AbiParser(propertiesMock)
	}

	// clean up the contractEventStore and contractAddresses after each test
	def cleanup() {
		parser.contractEventStore.clear()
		parser.contractAddresses.clear()
	}

	def "parseAbiContent should parse Truffle format ABI"() {
		given: "A Truffle format ABI JSON"
		def truffleAbi = '''{
            "contractName": "TestContract",
            "networks": {
                "1": {
                    "address": "******************************************"
                }
            },
            "abi": [
                {
                    "type": "event",
                    "name": "Transfer",
                    "inputs": [
                        {"indexed": true, "name": "from", "type": "address"},
                        {"indexed": true, "name": "to", "type": "address"},
                        {"indexed": false, "name": "value", "type": "uint256"}
                    ]
                }
            ]
        }'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		// Set environment variable for testing
		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should be correctly extracted"
		result != null
		result.address == "******************************************"
		result.name == "TestContract"
		result.lastModified == lastModified

		and: "Events should be parsed"
		parser.contractEventStore.size() > 0
		parser.contractAddresses.size() > 0
	}

	def "parseAbiContent should parse Hardhat format ABI"() {
		given: "A Hardhat format ABI JSON"
		def hardhatAbi = '''{
            "address": "******************************************",
            "abi": [
                {
                    "type": "event",
                    "name": "Approval",
                    "inputs": [
                        {"indexed": true, "name": "owner", "type": "address"},
                        {"indexed": true, "name": "spender", "type": "address"},
                        {"indexed": false, "name": "value", "type": "uint256"}
                    ]
                }
            ]
        }'''
		def inputStream = new ByteArrayInputStream(hardhatAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3001/TokenContract.json"
		def lastModified = new Date()

		// Mock the abiFormat to return a non-Truffle format
		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should be correctly extracted"
		result != null
		result.address == "******************************************"
		result.name == "TokenContract"
		result.lastModified == lastModified

		and: "Events should be parsed"
		parser.contractEventStore.size() == 1
		parser.contractAddresses.size() == 1
	}

	def "parseAbiContent should handle missing ABI section"() {
		given: "A JSON without ABI section"
		def invalidAbi = '''{
            "address": "******************************************",
            "something": "else"
        }'''
		def inputStream = new ByteArrayInputStream(invalidAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "An IOException should be thrown"
		thrown(IOException)
	}

	def "parseAbiContent should handle invalid JSON"() {
		given: "Invalid JSON content"
		def invalidJson = "{ not valid json }"
		def inputStream = new ByteArrayInputStream(invalidJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "An IOException should be thrown"
		thrown(IOException)
	}

	def "parseAbi should handle empty ABI JSON"() {
		given:
		String emptyAbi = "[]"

		when:
		parser.parseAbi(emptyAbi)

		then:

		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should skip entries with invalid event types"() {
		given:
		String abiWithInvalidType = '''[
        {
            "type": "invalid",
            "name": "InvalidEvent",
            "inputs": [
                {"indexed": true, "name": "data", "type": "address"}
            ]
        }
    ]'''

		when:
		parser.parseAbi(abiWithInvalidType)

		then:
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle events with missing name"() {
		given:
		String abiWithMissingName = '''[
        {
            "type": "event",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"},
                {"indexed": true, "name": "to", "type": "address"}
            ]
        }
    ]'''

		when:
		parser.parseAbi(abiWithMissingName)

		then:
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle events with empty inputs"() {
		given:
		String abiWithEmptyInputs = '''[
        {
            "type": "event",
            "name": "EmptyInputsEvent",
            "inputs": []
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithEmptyInputs)

		then:
		result.size() == 1
		result.iterator().next().value.name.equals("EmptyInputsEvent")
	}

	def "parseAbi should handle events with duplicate signatures"() {
		given:
		String abiWithDuplicateSignatures = '''[
        {
            "type": "event",
            "name": "DuplicateEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "DuplicateEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithDuplicateSignatures)

		then:
		result.size() == 1
		result.iterator().next().value.name.equals("DuplicateEvent")
	}

	def "parseAbi should handle mixed case Solidity types"() {
		given:
		String abiWithMixedCaseTypes = '''[
        {
            "type": "event",
            "name": "MixedCaseEvent",
            "inputs": [
                {"indexed": true, "name": "from", "type": "Address"},
                {"indexed": false, "name": "value", "type": "Uint256"}
            ]
        }
    ]'''

		when:
		def result = parser.parseAbi(abiWithMixedCaseTypes)

		then:
		result.size() == 1
		result.iterator().next().value.name.equals("MixedCaseEvent")
	}

	def "parseAbi should handle a large number of events"() {
		given:
		StringBuilder largeAbi = new StringBuilder("[")
		(1..1000).each { i ->
			largeAbi.append("""
        {
            "type": "event",
            "name": "Event$i",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"},
                {"indexed": false, "name": "value", "type": "uint256"}
            ]
        }""")
			if (i < 1000) {
				largeAbi.append(",")
			}
		}
		largeAbi.append("]")

		when:
		def result = parser.parseAbi(largeAbi.toString())

		then:

		result.size() == 1000
	}

	def "parseAbi should handle null or empty ABI content"() {
		when: "Parsing null ABI content"
		parser.parseAbi(null)

		then: "No events should be parsed"
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()

		when: "Parsing empty ABI content"
		parser.parseAbi("")

		then: "No events should be parsed"
		parser.contractEventStore.isEmpty()
		parser.contractAddresses.isEmpty()
	}

	def "parseAbi should handle invalid JSON"() {
		given: "Invalid JSON content"
		String invalidJson = "{ invalid json }"

		when: "Parsing the invalid JSON"
		parser.parseAbi(invalidJson)

		then: "An exception should be thrown"
		thrown(IOException)
	}

	def "parseAbi should handle events with unsupported types"() {
		given: "ABI with unsupported type"
		String abiWithUnsupportedType = '''[
            {
                "type": "event",
                "name": "UnsupportedTypeEvent",
                "inputs": [
                    {"indexed": true, "name": "data", "type": "unsupportedType"}
                ]
            }
        ]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiWithUnsupportedType)

		then: "No events should be parsed"
		!result.isEmpty()
	}

	def "parseAbiContent should handle missing address in networks"() {
		given: "Truffle ABI with missing address"
		def truffleAbi = '''{
            "contractName": "TestContract",
            "networks": {
                "1": {}
            },
            "abi": []
        }'''
		def inputStream = new ByteArrayInputStream(truffleAbi.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "parseAbiContent should handle missing contract name in object key"() {
		given: "ABI JSON with missing contract name"
		def abiJson = '''{
            "address": "******************************************",
            "abi": []
        }'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "hardhat"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty name"
		result.address == "******************************************"
		result.name == ""
		result.lastModified == lastModified
	}

	def "createTypeReference should handle unsupported types gracefully"() {
		when: "Creating a type reference for an unsupported type"
		def result = parser.createTypeReference("unsupportedType", false)

		then: "A default type reference should be returned"
		result != null
	}

	def "parseAbiContent should close input stream after parsing"() {
		given: "A valid ABI JSON"
		def abiJson = '''{
            "address": "******************************************",
            "abi": []
        }'''
		def inputStream = Mock(InputStream)
		inputStream.readAllBytes() >> abiJson.getBytes(StandardCharsets.UTF_8)

		when: "Parsing the ABI content"
		parser.parseAbiContent(inputStream, "3000/TestContract.json", new Date())

		then: "The input stream should be closed"
		1 * inputStream.close()
	}

	def "parseAbi should skip events with missing or empty names"() {
		given: "ABI JSON with events missing or having empty names"
		String abiWithMissingOrEmptyNames = '''[
        {
            "type": "event",
            "inputs": [
                {"indexed": true, "name": "from", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "",
            "inputs": [
                {"indexed": true, "name": "to", "type": "address"}
            ]
        },
        {
            "type": "event",
            "name": "1",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "string"}
            ]
        },
        {
            "type": "event",
            "name": "1.2",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bool"}
            ]
        },
        {
            "type": "event",
            "name": "1.3",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bytes"}
            ]
        },
        {
            "type": "event",
            "name": "1.4",
            "inputs": [
                {"indexed": "true", "name": "to", "type": "bytes32"}
            ]
        },
        {
            "type": "event",
            "name": "2",
            "inputs": [
                {"name": "to", "type": "address"}
            ]
        }
    ]'''

		when: "Parsing the ABI"
		def result = parser.parseAbi(abiWithMissingOrEmptyNames)

		then: "No events should be parsed"
		result.size() == 5
	}

	def "parseAbiContent should handle networksNode not being an object"() {
		given: "ABI JSON with networksNode as a non-object"
		def abiJson = '''{
        "contractName": "TestContract",
        "networks": "invalidNetworks",
        "abi": []
    }'''
		def inputStream = new ByteArrayInputStream(abiJson.getBytes(StandardCharsets.UTF_8))
		def objectKey = "3000/TestContract.json"
		def lastModified = new Date()

		propertiesMock.getAbiFormat() >> "truffle"

		when: "Parsing the ABI content"
		def result = parser.parseAbiContent(inputStream, objectKey, lastModified)

		then: "Contract info should have an empty address"
		result.address == ""
		result.name == "TestContract"
		result.lastModified == lastModified
	}

	def "appendContractAddress should add new addresses only once"() {
		given: "A contract address"
		def address = "******************************************"

		when: "Adding the address for the first time"
		parser.appendContractAddress(address)

		then: "The address should be added to the list"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.size() == 1

		when: "Adding the same address again"
		parser.appendContractAddress(address)

		then: "The address should not be added again"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.size() == 1

		when: "Adding a different address"
		def address2 = "0x0987654321098765432109876543210987654321"
		parser.appendContractAddress(address2)

		then: "The new address should be added"
		parser.contractAddresses.contains(address)
		parser.contractAddresses.contains(address2)
		parser.contractAddresses.size() == 2
	}

	def "getABIEventByLog should find and return event for valid log"() {
		given: "A log with valid address and event signature"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract events are in the store"
		def eventsMap = [(eventSignature.toLowerCase()): event]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "The correct event should be returned"
		result == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getABIEventByLog should throw exception when contract address not found"() {
		given: "A log with address not in the store"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "0xabcdef1234567890123456789012345678901234"

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract address is not in the store"
		assert !parser.contractEventStore.containsKey(contractAddress.toLowerCase())

		when: "Getting the event by log"
		parser.getABIEventByLog(log)

		then: "A ResourceNotFoundException should be thrown"
		def e = thrown(ResourceNotFoundException)
		e.message.contains("Event definition not found in ABI for event ID:")
		e.message.contains(eventSignature.toLowerCase())
		e.message.contains(contractAddress.toLowerCase())
	}

	def "getABIEventByLog should throw exception when event signature not found"() {
		given: "A log with valid address but unknown event signature"
		def knownSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def unknownSignature = "0xdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeef"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)

		Log log = new Log()
		log.topics = [unknownSignature]
		log.address = contractAddress

		and: "The contract events are in the store but without the requested signature"
		def eventsMap = [(knownSignature.toLowerCase()): event]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		parser.getABIEventByLog(log)

		then: "A ResourceNotFoundException should be thrown"
		def e = thrown(ResourceNotFoundException)
		e.message.contains("Event definition not found in ABI for event ID:")
		e.message.contains(unknownSignature.toLowerCase())
		e.message.contains(contractAddress.toLowerCase())

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getABIEventByLog should handle case-insensitive matching"() {
		given: "A log with uppercase address and event signature"
		def eventSignature = "0x1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF"
		def contractAddress = "******************************************"

		// Create a real Event instance
		List<TypeReference<?>> parameters = []
		def event = new Event("TestEvent", parameters)

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress.toUpperCase()

		and: "The contract events are in the store with lowercase keys"
		def eventsMap = [(eventSignature.toLowerCase()): event]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder().contractName("TestContract").events(eventsMap).build())

		when: "Getting the event by log"
		def result = parser.getABIEventByLog(log)

		then: "The correct event should be returned despite case differences"
		result == event

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getEventDefinitionByLog should find and return event definition for valid log"() {
		given: "A log with valid address and event signature"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "******************************************"

		// Create parameter info
		def paramInfo = ParameterInfo.builder()
			.name("testParam")
			.typeReference(TypeReference.create(Address.class))
			.indexed(true)
			.build()

		// Create Web3j event
		List<TypeReference<?>> parameters = [TypeReference.create(Address.class)]
		def web3jEvent = new Event("TestEvent", parameters)

		// Create event definition
		def eventDefinition = EventDefinition.builder()
			.name("TestEvent")
			.web3jEvent(web3jEvent)
			.parameters([paramInfo])
			.build()

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract event definitions are in the store"
		def eventDefinitionsMap = [(eventSignature.toLowerCase()): eventDefinition]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder()
					.contractName("TestContract")
					.events([:])
					.eventDefinitions(eventDefinitionsMap)
					.build())

		when: "Getting the event definition by log"
		def result = parser.getEventDefinitionByLog(log)

		then: "The correct event definition should be returned"
		result == eventDefinition
		result.name == "TestEvent"
		result.parameters.size() == 1
		result.parameters[0].name == "testParam"

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getEventDefinitionByLog should throw exception when contract address not found"() {
		given: "A log with address not in the store"
		def eventSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def contractAddress = "0xabcdef1234567890123456789012345678901234"

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress

		and: "The contract address is not in the store"
		assert !parser.contractEventStore.containsKey(contractAddress.toLowerCase())

		when: "Getting the event definition by log"
		parser.getEventDefinitionByLog(log)

		then: "A ResourceNotFoundException should be thrown"
		def e = thrown(ResourceNotFoundException)
		e.message.contains("Event definition not found for log:")
		e.message.contains(eventSignature.toLowerCase())
		e.message.contains(contractAddress.toLowerCase())
	}

	def "getEventDefinitionByLog should throw exception when event signature not found"() {
		given: "A log with valid address but unknown event signature"
		def knownSignature = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def unknownSignature = "0xdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeefdeadbeef"
		def contractAddress = "******************************************"

		// Create parameter info and event definition for known signature
		def paramInfo = ParameterInfo.builder()
			.name("testParam")
			.typeReference(TypeReference.create(Address.class))
			.indexed(true)
			.build()

		List<TypeReference<?>> parameters = [TypeReference.create(Address.class)]
		def web3jEvent = new Event("TestEvent", parameters)

		def eventDefinition = EventDefinition.builder()
			.name("TestEvent")
			.web3jEvent(web3jEvent)
			.parameters([paramInfo])
			.build()

		Log log = new Log()
		log.topics = [unknownSignature]
		log.address = contractAddress

		and: "The contract event definitions are in the store but without the requested signature"
		def eventDefinitionsMap = [(knownSignature.toLowerCase()): eventDefinition]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder()
					.contractName("TestContract")
					.events([:])
					.eventDefinitions(eventDefinitionsMap)
					.build())

		when: "Getting the event definition by log"
		parser.getEventDefinitionByLog(log)

		then: "A ResourceNotFoundException should be thrown"
		def e = thrown(ResourceNotFoundException)
		e.message.contains("Event definition not found for log:")
		e.message.contains(unknownSignature.toLowerCase())
		e.message.contains(contractAddress.toLowerCase())

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "getEventDefinitionByLog should handle case-insensitive matching"() {
		given: "A log with uppercase address and event signature"
		def eventSignature = "0x1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF"
		def contractAddress = "******************************************"

		// Create parameter info and event definition
		def paramInfo = ParameterInfo.builder()
			.name("testParam")
			.typeReference(TypeReference.create(Uint256.class))
			.indexed(false)
			.build()

		List<TypeReference<?>> parameters = [TypeReference.create(Uint256.class)]
		def web3jEvent = new Event("TestEvent", parameters)

		def eventDefinition = EventDefinition.builder()
			.name("TestEvent")
			.web3jEvent(web3jEvent)
			.parameters([paramInfo])
			.build()

		Log log = new Log()
		log.topics = [eventSignature]
		log.address = contractAddress.toUpperCase()

		and: "The contract event definitions are in the store with lowercase keys"
		def eventDefinitionsMap = [(eventSignature.toLowerCase()): eventDefinition]
		parser.contractEventStore.put(contractAddress.toLowerCase(),
				ContractEvents.builder()
					.contractName("TestContract")
					.events([:])
					.eventDefinitions(eventDefinitionsMap)
					.build())

		when: "Getting the event definition by log"
		def result = parser.getEventDefinitionByLog(log)

		then: "The correct event definition should be returned despite case differences"
		result == eventDefinition
		result.name == "TestEvent"
		result.parameters.size() == 1
		result.parameters[0].name == "testParam"
		result.parameters[0].indexed == false

		cleanup:
		parser.contractEventStore.remove(contractAddress.toLowerCase())
	}

	def "parseAbiWithParameterNames should handle null ABI content"() {
		when: "Parsing null ABI content"
		def result = parser.parseAbiWithParameterNames(null)

		then: "Should return empty map"
		result != null
		result.isEmpty()
	}

	def "parseAbiWithParameterNames should handle empty ABI content"() {
		when: "Parsing empty ABI content"
		def result = parser.parseAbiWithParameterNames("")

		then: "Should return empty map"
		result != null
		result.isEmpty()
	}

	def "parseAbiWithParameterNames should handle invalid JSON"() {
		given: "Invalid JSON content"
		def invalidJson = "{ invalid json content"

		when: "Parsing invalid JSON"
		parser.parseAbiWithParameterNames(invalidJson)

		then: "Should throw IOException"
		def e = thrown(IOException)
		e.message.contains("ABI parsing with parameter names failed")
	}

	def "parseAbiWithParameterNames should skip events with null name"() {
		given: "ABI with event having null name"
		def abiWithNullName = '''[
			{
				"type": "event",
				"inputs": [
					{"indexed": true, "name": "from", "type": "address"}
				]
			},
			{
				"type": "event",
				"name": "ValidEvent",
				"inputs": [
					{"indexed": false, "name": "value", "type": "uint256"}
				]
			}
		]'''

		when: "Parsing ABI with null name event"
		def result = parser.parseAbiWithParameterNames(abiWithNullName)

		then: "Should skip null name event and process valid event"
		result != null
		result.size() == 1
		result.values().any { it.name == "ValidEvent" }
	}

	def "parseAbiWithParameterNames should skip events with empty name"() {
		given: "ABI with event having empty name"
		def abiWithEmptyName = '''[
			{
				"type": "event",
				"name": "",
				"inputs": [
					{"indexed": true, "name": "from", "type": "address"}
				]
			},
			{
				"type": "event",
				"name": "ValidEvent",
				"inputs": [
					{"indexed": false, "name": "value", "type": "uint256"}
				]
			}
		]'''

		when: "Parsing ABI with empty name event"
		def result = parser.parseAbiWithParameterNames(abiWithEmptyName)

		then: "Should skip empty name event and process valid event"
		result != null
		result.size() == 1
		result.values().any { it.name == "ValidEvent" }
	}

	def "parseAbiWithParameterNames should handle events with missing parameter names"() {
		given: "ABI with event having parameters without names"
		def abiWithMissingParamNames = '''[
			{
				"type": "event",
				"name": "TestEvent",
				"inputs": [
					{"indexed": true, "type": "address"},
					{"indexed": false, "name": "value", "type": "uint256"},
					{"indexed": false, "type": "string"}
				]
			}
		]'''

		when: "Parsing ABI with missing parameter names"
		def result = parser.parseAbiWithParameterNames(abiWithMissingParamNames)

		then: "Should generate default parameter names"
		result != null
		result.size() == 1
		def eventDef = result.values().first()
		eventDef.name == "TestEvent"
		eventDef.parameters.size() == 3

		// Check that default names are generated for missing names
		def paramNames = eventDef.parameters.collect { it.name }
		paramNames.contains("param0") // First param without name
		paramNames.contains("value")  // Named param
		paramNames.contains("param2") // Third param without name
	}

	def "parseAbiWithParameterNames should handle events with all parameter types"() {
		given: "ABI with event having various parameter types"
		def abiWithVariousTypes = '''[
			{
				"type": "event",
				"name": "ComplexEvent",
				"inputs": [
					{"indexed": true, "name": "addr", "type": "address"},
					{"indexed": true, "name": "id", "type": "uint256"},
					{"indexed": false, "name": "data", "type": "bytes32"},
					{"indexed": false, "name": "text", "type": "string"},
					{"indexed": false, "name": "flag", "type": "bool"}
				]
			}
		]'''

		when: "Parsing ABI with various parameter types"
		def result = parser.parseAbiWithParameterNames(abiWithVariousTypes)

		then: "Should correctly parse all parameter types"
		result != null
		result.size() == 1
		def eventDef = result.values().first()
		eventDef.name == "ComplexEvent"
		eventDef.parameters.size() == 5

		// Check indexed parameters
		def indexedParams = eventDef.getIndexedParameters()
		indexedParams.size() == 2
		indexedParams.any { it.name == "addr" && it.indexed }
		indexedParams.any { it.name == "id" && it.indexed }

		// Check non-indexed parameters
		def nonIndexedParams = eventDef.getNonIndexedParameters()
		nonIndexedParams.size() == 3
		nonIndexedParams.any { it.name == "data" && !it.indexed }
		nonIndexedParams.any { it.name == "text" && !it.indexed }
		nonIndexedParams.any { it.name == "flag" && !it.indexed }
	}
}
